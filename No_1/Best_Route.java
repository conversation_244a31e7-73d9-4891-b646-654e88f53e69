import java.util.*;
import java.io.*;

public class Best_Route {
    static class Edge {
        int to;
        int weight;

        Edge(int to, int weight) {
            this.to = to;
            this.weight = weight;
        }
    }

    static class Node implements Comparable<Node> {
        int id;
        int distance;

        Node(int id, int distance) {
            this.id = id;
            this.distance = distance;
        }

        @Override
        public int compareTo(Node other) {
            return Integer.compare(this.distance, other.distance);
        }
    }

    public static void main(String[] args) throws IOException {
        BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
        String line;
        while ((line = br.readLine()) != null && !line.isEmpty()) {
            StringTokenizer st = new StringTokenizer(line);
            int n = Integer.parseInt(st.nextToken());
            int m = Integer.parseInt(st.nextToken());
            int s = Integer.parseInt(st.nextToken());

            // 构建邻接表（有向图）
            List<List<Edge>> graph = new ArrayList<>();
            for (int i = 0; i <= n; i++) {
                graph.add(new ArrayList<>());
            }

            // 读取边信息
            for (int i = 0; i < m; i++) {
                st = new StringTokenizer(br.readLine());
                int p = Integer.parseInt(st.nextToken());
                int q = Integer.parseInt(st.nextToken());
                int t = Integer.parseInt(st.nextToken());
                graph.get(p).add(new Edge(q, t));
            }

            // 读取起点集合
            int w = Integer.parseInt(br.readLine());
            int[] starts = new int[w];
            st = new StringTokenizer(br.readLine());
            for (int i = 0; i < w; i++) {
                starts[i] = Integer.parseInt(st.nextToken());
            }

            // 计算从每个起点到终点的最短路径
            int minTime = Integer.MAX_VALUE;
            for (int start : starts) {
                int time = dijkstra(graph, start, s, n);
                if (time != -1 && time < minTime) {
                    minTime = time;
                }
            }

            System.out.println(minTime == Integer.MAX_VALUE ? -1 : minTime);
        }
    }

    private static int dijkstra(List<List<Edge>> graph, int start, int end, int n) {
        int[] dist = new int[n + 1];
        Arrays.fill(dist, Integer.MAX_VALUE);
        dist[start] = 0;

        PriorityQueue<Node> pq = new PriorityQueue<>();
        pq.offer(new Node(start, 0));

        while (!pq.isEmpty()) {
            Node node = pq.poll();
            int u = node.id;

            if (u == end) {
                return dist[u];
            }

            if (node.distance > dist[u]) {
                continue;
            }

            for (Edge edge : graph.get(u)) {
                int v = edge.to;
                int newDist = dist[u] + edge.weight;
                if (newDist < dist[v]) {
                    dist[v] = newDist;
                    pq.offer(new Node(v, newDist));
                }
            }
        }

        return -1;
    }
}