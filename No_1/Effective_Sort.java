import java.util.*;
import java.time.LocalDate;

class Student {
    private String name;
    private LocalDate birthday;

    public Student(String name, int year, int month, int day) {
        this.name = name;
        try {
            this.birthday = LocalDate.of(year, month, day);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date: " + year + "-" + month + "-" + day);
        }
    }

    public String getName() {
        return name;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    @Override
    public String toString() {
        return name + " " + birthday.getYear() + " " + birthday.getMonthValue() + " " + birthday.getDayOfMonth();
    }
}

public class Effective_Sort {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // 读取第一行输入
        String[] firstLine = scanner.nextLine().trim().split("\\s+");
        int n = Integer.parseInt(firstLine[0]);
        String type = firstLine[1];

        // 读取数据行
        String[] data = scanner.nextLine().trim().split("\\s+");

        if (type.equals("Integer")) {
            // 处理Integer数组
            Integer[] numbers = new Integer[n];
            for (int i = 0; i < n; i++) {
                numbers[i] = Integer.parseInt(data[i]);
            }

            // 排序并输出
            Arrays.sort(numbers);
            for (int i = 0; i < n; i++) {
                System.out.print(numbers[i]);
                if (i < n - 1)
                    System.out.print(" ");
            }
        } else if (type.startsWith("Student")) {
            // 处理Student数组
            String sortBy = "name"; // 默认按name排序
            if (firstLine.length > 2) {
                sortBy = firstLine[2];
            }

            Student[] students = new Student[n];

            for (int i = 0; i < n; i++) {
                int base = i * 4;
                String name = data[base];
                int year = Integer.parseInt(data[base + 1]);
                int month = Integer.parseInt(data[base + 2]);
                int day = Integer.parseInt(data[base + 3]);
                students[i] = new Student(name, year, month, day);
            }

            // 根据指定字段排序
            if (sortBy.equals("birthday")) {
                Arrays.sort(students, Comparator.comparing(Student::getBirthday)
                        .thenComparing(Student::getName));
            } else {
                Arrays.sort(students, Comparator.comparing(Student::getName)
                        .thenComparing(Student::getBirthday));
            }

            // 输出结果
            for (int i = 0; i < n; i++) {
                System.out.print(students[i]);
                if (i < n - 1)
                    System.out.print(" ");
            }
        }

        System.out.println();
    }
}