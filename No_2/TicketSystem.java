import java.util.*;

/**
 * 公园门票系统 - 策略模式实现
 */

// 折扣策略接口
interface Discount {
    int calculatePrice(int originalPrice);
}

// 票类型策略接口
interface Ticket {
    String getTicketType();
}

// 具体折扣策略类
class StudentDiscount implements Discount {
    @Override
    public int calculatePrice(int originalPrice) {
        return originalPrice * 8 / 10; // 学生打8折
    }
}

class ChildrenDiscount implements Discount {
    @Override
    public int calculatePrice(int originalPrice) {
        return originalPrice - 30; // 儿童减30
    }
}

class SoldierDiscount implements Discount {
    @Override
    public int calculatePrice(int originalPrice) {
        return 0; // 军人免费
    }
}

class AdultDiscount implements Discount {
    @Override
    public int calculatePrice(int originalPrice) {
        return originalPrice; // 其他原价
    }
}

// 具体票类型策略类
class PaperTicket implements Ticket {
    @Override
    public String getTicketType() {
        return "PaperTicket";
    }
}

class ElectronicalTicket implements Ticket {
    @Override
    public String getTicketType() {
        return "E_ticket";
    }
}

// 公园门票类（环境类）
class ParkTicket {
    private Discount discount;
    private Ticket ticket;
    
    public ParkTicket(Discount discount, Ticket ticket) {
        this.discount = discount;
        this.ticket = ticket;
    }
    
    public void setDiscount(Discount discount) {
        this.discount = discount;
    }
    
    public void setTicket(Ticket ticket) {
        this.ticket = ticket;
    }
    
    public int getFinalPrice(int originalPrice) {
        return discount.calculatePrice(originalPrice);
    }
    
    public String getTicketInfo() {
        return ticket.getTicketType();
    }
}

public class TicketSystem {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // 读取原始票价
        int originalPrice = scanner.nextInt();
        
        // 读取测试用例数量
        int n = scanner.nextInt();
        
        for (int i = 0; i < n; i++) {
            String customerType = scanner.next();
            String ticketType = scanner.next();
            
            // 根据客户类型选择折扣策略
            Discount discount;
            switch (customerType) {
                case "student":
                    discount = new StudentDiscount();
                    break;
                case "children":
                    discount = new ChildrenDiscount();
                    break;
                case "soldier":
                    discount = new SoldierDiscount();
                    break;
                case "adult":
                default:
                    discount = new AdultDiscount();
                    break;
            }
            
            // 根据票类型选择票策略
            Ticket ticket;
            switch (ticketType) {
                case "paper":
                    ticket = new PaperTicket();
                    break;
                case "electronical":
                default:
                    ticket = new ElectronicalTicket();
                    break;
            }
            
            // 创建公园门票对象
            ParkTicket parkTicket = new ParkTicket(discount, ticket);
            
            // 输出结果
            System.out.println(parkTicket.getTicketInfo());
            System.out.println("Price:" + parkTicket.getFinalPrice(originalPrice));
        }
        
        scanner.close();
    }
}
