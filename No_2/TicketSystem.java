import java.util.*;

/**
 * 公园门票销售系统 - 使用策略设计模式
 */

// 价格计算策略接口
interface PriceStrategy {
    int computeTicketPrice(int basePrice);
}

// 门票类型策略接口
interface TicketTypeStrategy {
    String generateTicketFormat();
}

// 学生价格策略实现
class StudentPriceStrategy implements PriceStrategy {
    @Override
    public int computeTicketPrice(int basePrice) {
        return (int) (basePrice * 0.8); // 学生享受8折优惠
    }
}

// 儿童价格策略实现
class ChildPriceStrategy implements PriceStrategy {
    @Override
    public int computeTicketPrice(int basePrice) {
        return Math.max(0, basePrice - 30); // 儿童票减免30元
    }
}

// 军人价格策略实现
class MilitaryPriceStrategy implements PriceStrategy {
    @Override
    public int computeTicketPrice(int basePrice) {
        return 0; // 军人享受免费政策
    }
}

// 成人价格策略实现
class RegularPriceStrategy implements PriceStrategy {
    @Override
    public int computeTicketPrice(int basePrice) {
        return basePrice; // 成人按原价收费
    }
}

// 纸质票类型策略实现
class PhysicalTicketStrategy implements TicketTypeStrategy {
    @Override
    public String generateTicketFormat() {
        return "PaperTicket";
    }
}

// 电子票类型策略实现
class DigitalTicketStrategy implements TicketTypeStrategy {
    @Override
    public String generateTicketFormat() {
        return "E_ticket";
    }
}

// 门票销售管理器（环境上下文类）
class TicketSalesManager {
    private PriceStrategy priceCalculator;
    private TicketTypeStrategy ticketFormatter;

    public TicketSalesManager(PriceStrategy priceCalculator, TicketTypeStrategy ticketFormatter) {
        this.priceCalculator = priceCalculator;
        this.ticketFormatter = ticketFormatter;
    }

    public void updatePriceStrategy(PriceStrategy newPriceCalculator) {
        this.priceCalculator = newPriceCalculator;
    }

    public void updateTicketTypeStrategy(TicketTypeStrategy newTicketFormatter) {
        this.ticketFormatter = newTicketFormatter;
    }

    public int calculateFinalPrice(int basePrice) {
        return priceCalculator.computeTicketPrice(basePrice);
    }

    public String getTicketTypeInfo() {
        return ticketFormatter.generateTicketFormat();
    }
}

public class TicketSystem {
    public static void main(String[] args) {
        Scanner inputReader = new Scanner(System.in);

        // 获取基础门票价格
        int baseTicketPrice = inputReader.nextInt();

        // 获取测试案例总数
        int testCaseCount = inputReader.nextInt();

        // 处理每个测试案例
        for (int caseIndex = 0; caseIndex < testCaseCount; caseIndex++) {
            String visitorCategory = inputReader.next();
            String ticketFormat = inputReader.next();

            // 根据访客类别选择价格计算策略
            PriceStrategy selectedPriceStrategy;
            if ("student".equals(visitorCategory)) {
                selectedPriceStrategy = new StudentPriceStrategy();
            } else if ("children".equals(visitorCategory)) {
                selectedPriceStrategy = new ChildPriceStrategy();
            } else if ("soldier".equals(visitorCategory)) {
                selectedPriceStrategy = new MilitaryPriceStrategy();
            } else { // "adult" 或其他情况
                selectedPriceStrategy = new RegularPriceStrategy();
            }

            // 根据票据格式选择票据类型策略
            TicketTypeStrategy selectedTicketStrategy;
            if ("paper".equals(ticketFormat)) {
                selectedTicketStrategy = new PhysicalTicketStrategy();
            } else { // "electronical" 或其他情况
                selectedTicketStrategy = new DigitalTicketStrategy();
            }

            // 创建门票销售管理器实例
            TicketSalesManager salesManager = new TicketSalesManager(selectedPriceStrategy, selectedTicketStrategy);

            // 输出处理结果
            System.out.println(salesManager.getTicketTypeInfo());
            System.out.println("Price:" + salesManager.calculateFinalPrice(baseTicketPrice));
        }

        inputReader.close();
    }
}
