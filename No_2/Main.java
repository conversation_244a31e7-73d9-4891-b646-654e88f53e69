import java.util.Scanner;

/**
 * 分数类，实现分数的基本运算
 */
public class Main {
    private int numerator; // 分子
    private int denominator; // 分母

    /**
     * 构造函数
     *
     * @param numerator   分子
     * @param denominator 分母
     */
    public Main(int numerator, int denominator) {
        if (denominator == 0) {
            throw new IllegalArgumentException("分母不能为0");
        }
        this.numerator = numerator;
        this.denominator = denominator;
        simplify(); // 化简分数
    }

    /**
     * 求最大公约数
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return 最大公约数
     */
    private int gcd(int a, int b) {
        a = Math.abs(a);
        b = Math.abs(b);
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }

    /**
     * 化简分数
     */
    private void simplify() {
        int gcd = gcd(numerator, denominator);
        numerator /= gcd;
        denominator /= gcd;

        // 确保分母为正数
        if (denominator < 0) {
            numerator = -numerator;
            denominator = -denominator;
        }
    }

    /**
     * 分数加法
     *
     * @param other 另一个分数
     * @return 相加结果
     */
    public Main add(Main other) {
        int newNumerator = this.numerator * other.denominator + other.numerator * this.denominator;
        int newDenominator = this.denominator * other.denominator;
        return new Main(newNumerator, newDenominator);
    }

    /**
     * 分数减法
     *
     * @param other 另一个分数
     * @return 相减结果
     */
    public Main subtract(Main other) {
        int newNumerator = this.numerator * other.denominator - other.numerator * this.denominator;
        int newDenominator = this.denominator * other.denominator;
        return new Main(newNumerator, newDenominator);
    }

    /**
     * 分数乘法
     *
     * @param other 另一个分数
     * @return 相乘结果
     */
    public Main multiply(Main other) {
        int newNumerator = this.numerator * other.numerator;
        int newDenominator = this.denominator * other.denominator;
        return new Main(newNumerator, newDenominator);
    }

    /**
     * 分数除法
     *
     * @param other 另一个分数
     * @return 相除结果
     */
    public Main divide(Main other) {
        if (other.numerator == 0) {
            throw new IllegalArgumentException("不能除以0");
        }
        int newNumerator = this.numerator * other.denominator;
        int newDenominator = this.denominator * other.numerator;
        return new Main(newNumerator, newDenominator);
    }

    /**
     * 获取分数的倒数
     *
     * @return 倒数
     */
    public Main reciprocal() {
        if (numerator == 0) {
            throw new IllegalArgumentException("0没有倒数");
        }
        return new Main(denominator, numerator);
    }

    /**
     * 获取分数的小数值
     *
     * @return 小数值
     */
    public double getValue() {
        return (double) numerator / denominator;
    }

    /**
     * 获取分数的小数值，保留1位小数
     *
     * @return 格式化的小数字符串
     */
    public String getFormattedValue() {
        double value = (double) numerator / denominator;
        return String.format("%.1f", value);
    }

    /**
     * 返回分数的字符串表示
     *
     * @return 分数字符串
     */
    @Override
    public String toString() {
        return numerator + "/" + denominator;
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // 读取输入
        int num1 = scanner.nextInt();
        int den1 = scanner.nextInt();
        int num2 = scanner.nextInt();
        int den2 = scanner.nextInt();

        // 创建两个分数对象
        Main f1 = new Main(num1, den1);
        Main f2 = new Main(num2, den2);

        // 按题目要求输出8行：a, b, a+b, a-b, a*b, a/b, 1/a, a(小数形式，保留1位小数)
        System.out.println(f1); // a
        System.out.println(f2); // b
        System.out.println(f1.add(f2)); // a+b
        System.out.println(f1.subtract(f2)); // a-b
        System.out.println(f1.multiply(f2)); // a*b
        System.out.println(f1.divide(f2)); // a/b
        System.out.println(f1.reciprocal()); // 1/a
        System.out.println(f1.getFormattedValue()); // a(小数形式，保留1位小数)

        scanner.close();
    }
}
