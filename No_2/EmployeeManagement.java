import java.util.*;

/**
 * 企业人力资源管理系统
 */

// 抽象工作人员基类
abstract class WorkerBase implements Comparable<WorkerBase> {
    protected String givenName;
    protected String familyName;
    protected String identificationCode;

    public WorkerBase(String givenName, String familyName, String identificationCode) {
        this.givenName = givenName;
        this.familyName = familyName;
        this.identificationCode = identificationCode;
    }

    // 访问器和修改器方法
    public String retrieveGivenName() {
        return givenName;
    }

    public void updateGivenName(String givenName) {
        this.givenName = givenName;
    }

    public String retrieveFamilyName() {
        return familyName;
    }

    public void updateFamilyName(String familyName) {
        this.familyName = familyName;
    }

    public String retrieveIdentificationCode() {
        return identificationCode;
    }

    // 抽象方法：计算月度薪酬
    public abstract double calculateMonthlySalary();

    @Override
    public String toString() {
        return String.format("firstName:%s; lastName:%s; socialSecurityNumber:%s; earning:%.2f",
                givenName, familyName, identificationCode, calculateMonthlySalary());
    }

    @Override
    public int compareTo(WorkerBase otherWorker) {
        return Double.compare(this.calculateMonthlySalary(), otherWorker.calculateMonthlySalary());
    }
}

// 固定薪资工作者类
class WeeklySalaryWorker extends WorkerBase {
    private double weeklyPayment;

    public WeeklySalaryWorker(String givenName, String familyName, String identificationCode, double weeklyPayment) {
        super(givenName, familyName, identificationCode);
        this.weeklyPayment = weeklyPayment;
    }

    public double retrieveWeeklyPayment() {
        return weeklyPayment;
    }

    public void modifyWeeklyPayment(double weeklyPayment) {
        this.weeklyPayment = weeklyPayment;
    }

    @Override
    public double calculateMonthlySalary() {
        return weeklyPayment * 4;
    }
}

// 按时计酬工作者类
class HourlyWageWorker extends WorkerBase {
    private double hourlyRate;
    private double workingHours;

    public HourlyWageWorker(String givenName, String familyName, String identificationCode, double hourlyRate,
            double workingHours) {
        super(givenName, familyName, identificationCode);
        this.hourlyRate = hourlyRate;
        this.workingHours = workingHours;
    }

    public double retrieveHourlyRate() {
        return hourlyRate;
    }

    public void modifyHourlyRate(double hourlyRate) {
        this.hourlyRate = hourlyRate;
    }

    public double retrieveWorkingHours() {
        return workingHours;
    }

    public void modifyWorkingHours(double workingHours) {
        this.workingHours = workingHours;
    }

    @Override
    public double calculateMonthlySalary() {
        return hourlyRate * workingHours;
    }
}

// 销售提成工作者类
class CommissionBasedWorker extends WorkerBase {
    private double totalSalesAmount;
    private double commissionPercentage;

    public CommissionBasedWorker(String givenName, String familyName, String identificationCode,
            double totalSalesAmount, double commissionPercentage) {
        super(givenName, familyName, identificationCode);
        this.totalSalesAmount = totalSalesAmount;
        this.commissionPercentage = commissionPercentage;
    }

    public double retrieveTotalSalesAmount() {
        return totalSalesAmount;
    }

    public void modifyTotalSalesAmount(double totalSalesAmount) {
        this.totalSalesAmount = totalSalesAmount;
    }

    public double retrieveCommissionPercentage() {
        return commissionPercentage;
    }

    public void modifyCommissionPercentage(double commissionPercentage) {
        this.commissionPercentage = commissionPercentage;
    }

    @Override
    public double calculateMonthlySalary() {
        return totalSalesAmount * commissionPercentage;
    }
}

// 底薪加提成工作者类
class BasePayPlusCommissionWorker extends CommissionBasedWorker {
    private double monthlyBasePay;

    public BasePayPlusCommissionWorker(String givenName, String familyName, String identificationCode,
            double totalSalesAmount, double commissionPercentage, double monthlyBasePay) {
        super(givenName, familyName, identificationCode, totalSalesAmount, commissionPercentage);
        this.monthlyBasePay = monthlyBasePay;
    }

    public double retrieveMonthlyBasePay() {
        return monthlyBasePay;
    }

    public void modifyMonthlyBasePay(double monthlyBasePay) {
        this.monthlyBasePay = monthlyBasePay;
    }

    @Override
    public double calculateMonthlySalary() {
        return super.calculateMonthlySalary() + monthlyBasePay;
    }
}

public class EmployeeManagement {
    public static void main(String[] args) {
        Scanner inputScanner = new Scanner(System.in);

        // 读取工作人员数量
        int totalWorkerCount = inputScanner.nextInt();
        WorkerBase[] workerArray = new WorkerBase[totalWorkerCount];

        // 读取工作人员信息
        for (int index = 0; index < totalWorkerCount; index++) {
            int workerType = inputScanner.nextInt();
            String givenName = inputScanner.next();
            String familyName = inputScanner.next();
            String identificationCode = inputScanner.next();

            switch (workerType) {
                case 0: // WeeklySalaryWorker
                    double weeklyPayment = inputScanner.nextDouble();
                    workerArray[index] = new WeeklySalaryWorker(givenName, familyName, identificationCode,
                            weeklyPayment);
                    break;
                case 1: // HourlyWageWorker
                    double hourlyRate = inputScanner.nextDouble();
                    double workingHours = inputScanner.nextDouble();
                    workerArray[index] = new HourlyWageWorker(givenName, familyName, identificationCode, hourlyRate,
                            workingHours);
                    break;
                case 2: // CommissionBasedWorker
                    double totalSalesAmount = inputScanner.nextDouble();
                    double commissionPercentage = inputScanner.nextDouble();
                    workerArray[index] = new CommissionBasedWorker(givenName, familyName, identificationCode,
                            totalSalesAmount,
                            commissionPercentage);
                    break;
                case 3: // BasePayPlusCommissionWorker
                    double salesAmount = inputScanner.nextDouble();
                    double commissionRate = inputScanner.nextDouble();
                    double monthlyBasePay = inputScanner.nextDouble();
                    workerArray[index] = new BasePayPlusCommissionWorker(givenName, familyName, identificationCode,
                            salesAmount,
                            commissionRate, monthlyBasePay);
                    break;
            }
        }

        // 读取查询数量
        int queryCount = inputScanner.nextInt();

        // 处理查询请求
        for (int queryIndex = 0; queryIndex < queryCount; queryIndex++) {
            int searchType = inputScanner.nextInt();
            String searchValue = inputScanner.next();

            List<WorkerBase> searchResults = new ArrayList<>();

            if (searchType == 0) { // 根据givenName查询
                for (WorkerBase worker : workerArray) {
                    if (worker.retrieveGivenName().equals(searchValue)) {
                        searchResults.add(worker);
                    }
                }
            } else if (searchType == 1) { // 根据identificationCode查询
                for (WorkerBase worker : workerArray) {
                    if (worker.retrieveIdentificationCode().equals(searchValue)) {
                        searchResults.add(worker);
                    }
                }
            }

            // 按月度薪酬从低到高排序
            Collections.sort(searchResults);

            // 输出查询结果
            for (WorkerBase worker : searchResults) {
                System.out.println(worker.toString());
            }
        }

        inputScanner.close();
    }
}
