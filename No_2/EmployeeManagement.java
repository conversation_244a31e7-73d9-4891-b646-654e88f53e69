import java.util.*;

/**
 * 员工管理系统
 */

// 抽象员工类
abstract class Employee implements Comparable<Employee> {
    private String firstName;
    private String lastName;
    private String socialSecurityNumber;

    public Employee(String firstName, String lastName, String socialSecurityNumber) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.socialSecurityNumber = socialSecurityNumber;
    }

    // getter和setter方法
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getSocialSecurityNumber() {
        return socialSecurityNumber;
    }

    // 抽象方法：计算月工资
    public abstract double earning();

    @Override
    public String toString() {
        return String.format("firstName:%s; lastName:%s; socialSecurityNumber:%s; earning:%.2f",
                firstName, lastName, socialSecurityNumber, earning());
    }

    @Override
    public int compareTo(Employee other) {
        return Double.compare(this.earning(), other.earning());
    }
}

// 周薪员工类
class SalaridEmployee extends Employee {
    private double weeklySalary;

    public SalaridEmployee(String firstName, String lastName, String socialSecurityNumber, double weeklySalary) {
        super(firstName, lastName, socialSecurityNumber);
        this.weeklySalary = weeklySalary;
    }

    public double getWeeklySalary() {
        return weeklySalary;
    }

    public void setWeeklySalary(double weeklySalary) {
        this.weeklySalary = weeklySalary;
    }

    @Override
    public double earning() {
        return weeklySalary * 4;
    }
}

// 小时工员工类
class HourlyEmployee extends Employee {
    private double wage;
    private double hours;

    public HourlyEmployee(String firstName, String lastName, String socialSecurityNumber, double wage, double hours) {
        super(firstName, lastName, socialSecurityNumber);
        this.wage = wage;
        this.hours = hours;
    }

    public double getWage() {
        return wage;
    }

    public void setWage(double wage) {
        this.wage = wage;
    }

    public double getHours() {
        return hours;
    }

    public void setHours(double hours) {
        this.hours = hours;
    }

    @Override
    public double earning() {
        return wage * hours;
    }
}

// 提成员工类
class CommisionEmployee extends Employee {
    private double grossSales;
    private double commissionRate;

    public CommisionEmployee(String firstName, String lastName, String socialSecurityNumber,
            double grossSales, double commissionRate) {
        super(firstName, lastName, socialSecurityNumber);
        this.grossSales = grossSales;
        this.commissionRate = commissionRate;
    }

    public double getGrossSales() {
        return grossSales;
    }

    public void setGrossSales(double grossSales) {
        this.grossSales = grossSales;
    }

    public double getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(double commissionRate) {
        this.commissionRate = commissionRate;
    }

    @Override
    public double earning() {
        return grossSales * commissionRate;
    }
}

// 底薪加提成员工类
class BasePlusCommisionEmployee extends CommisionEmployee {
    private double baseSalary;

    public BasePlusCommisionEmployee(String firstName, String lastName, String socialSecurityNumber,
            double grossSales, double commissionRate, double baseSalary) {
        super(firstName, lastName, socialSecurityNumber, grossSales, commissionRate);
        this.baseSalary = baseSalary;
    }

    public double getBaseSalary() {
        return baseSalary;
    }

    public void setBaseSalary(double baseSalary) {
        this.baseSalary = baseSalary;
    }

    @Override
    public double earning() {
        return super.earning() + baseSalary;
    }
}

public class EmployeeManagement {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // 读取员工数量
        int n = scanner.nextInt();
        Employee[] employees = new Employee[n];

        // 读取员工信息
        for (int i = 0; i < n; i++) {
            int type = scanner.nextInt();
            String firstName = scanner.next();
            String lastName = scanner.next();
            String socialSecurityNumber = scanner.next();

            switch (type) {
                case 0: // SalaridEmployee
                    double weeklySalary = scanner.nextDouble();
                    employees[i] = new SalaridEmployee(firstName, lastName, socialSecurityNumber, weeklySalary);
                    break;
                case 1: // HourlyEmployee
                    double wage = scanner.nextDouble();
                    double hours = scanner.nextDouble();
                    employees[i] = new HourlyEmployee(firstName, lastName, socialSecurityNumber, wage, hours);
                    break;
                case 2: // CommisionEmployee
                    double grossSales = scanner.nextDouble();
                    double commissionRate = scanner.nextDouble();
                    employees[i] = new CommisionEmployee(firstName, lastName, socialSecurityNumber, grossSales,
                            commissionRate);
                    break;
                case 3: // BasePlusCommisionEmployee
                    double grossSales2 = scanner.nextDouble();
                    double commissionRate2 = scanner.nextDouble();
                    double baseSalary = scanner.nextDouble();
                    employees[i] = new BasePlusCommisionEmployee(firstName, lastName, socialSecurityNumber, grossSales2,
                            commissionRate2, baseSalary);
                    break;
            }
        }

        // 读取查询数量
        int m = scanner.nextInt();

        // 处理查询
        for (int i = 0; i < m; i++) {
            int queryType = scanner.nextInt();
            String queryValue = scanner.next();

            List<Employee> results = new ArrayList<>();

            if (queryType == 0) { // 根据firstName查询
                for (Employee emp : employees) {
                    if (emp.getFirstName().equals(queryValue)) {
                        results.add(emp);
                    }
                }
            } else if (queryType == 1) { // 根据socialSecurityNumber查询
                for (Employee emp : employees) {
                    if (emp.getSocialSecurityNumber().equals(queryValue)) {
                        results.add(emp);
                    }
                }
            }

            // 按月工资从低到高排序
            Collections.sort(results);

            // 输出结果
            for (Employee emp : results) {
                System.out.println(emp.toString());
            }
        }

        scanner.close();
    }
}
